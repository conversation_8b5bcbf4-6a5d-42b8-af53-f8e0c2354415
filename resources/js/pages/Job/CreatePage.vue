<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3';
import { useI18n } from '@/composables/useI18n.ts';
import { route } from 'ziggy-js';
import { CreateJobProps, JobFormType } from '@/types/job.ts';
import { ref } from 'vue';
import JobForm from '@/pages/Job/common/JobForm.vue';
import Modal from '@/components/common/Modal.vue';
import { useValidation } from '@/composables/useValidation';

defineProps<CreateJobProps>();

const { t } = useI18n();

const {
  form,
  isPreview: isPreviewCreate,
  validateBeforePreview,
  setPreview,
} = useValidation<JobFormType>({
  recruitment_type: '',
  thumbnail: null,
  images: [],
  category_id: 0,
  employer_email: '',
  employer_name: '',
  employer_phone_number: '',
  type: '',
  prefecture: '',
  address: '',
  title: '',
  description: '',
  benefits: '',
  certificate_level: '',
  time_start: '',
  time_end: '',
  salary_type: '',
  salary: 0,
  travel_fee_type: '',
  travel_fee: 0,
  age: 0,
  gender: '',
  expired_at: '',
  job_start_at: '',
  is_instant: false,
  is_public: false,
});

function handlePreview() {
  validateBeforePreview({
    recruitment_type: ['required'],
    thumbnail: ['required'],
    images: ['required'],
    category_id: ['required'],
    employer_email: ['required'],
    employer_name: ['required'],
    employer_phone_number: ['required'],
    type: ['required'],
    prefecture: ['required'],
    address: ['required'],
    title: ['required'],
    description: ['required'],
    benefits: ['nullable'],
    certificate_level: ['nullable'],
    time_start: ['required'],
    time_end: ['required'],
    salary_type: ['required'],
    salary: ['required'],
    travel_fee_type: ['required'],
    travel_fee: ['required'],
    age: ['required'],
    gender: ['required'],
    expired_at: ['required'],
    job_start_at: ['required'],
    is_instant: ['required'],
    is_public: ['required'],
  });
}

function store() {
  form.post(route('admin.job.store'), {
    onSuccess: () => {
      isPreviewCreate.value = false;
    },
  });
}
</script>

<template>
  <Head :title="t('models/job.screenName.create')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/job.screenName.create') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <JobForm :categories="categories" :form-data="form" @openPreview="handlePreview()" />
  </div>
  <Modal v-if="isPreviewCreate" @close="isPreviewCreate = false" :title="t('models/job.screenName.create')">
    <template #body>
      <JobForm
        :categories="categories"
        :form-data="form"
        @submit="store"
        @openPreview="isPreviewCreate = true"
        @close="isPreviewCreate = false"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>

<style scoped></style>
